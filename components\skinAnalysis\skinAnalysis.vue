<template>
	<view style="transition: all 0.3s;justify-content: center;position: relative;" class="w-full flex-1 flex flex-col">
		<image ref="beforeimgRef" :src="props.peopleImg" mode="widthFix" style="width: 100%;" @load="onImageLoad" />
		<image class="regionalImg" :src="regionalImg" mode="widthFix" v-if="regionalImg!=''"
			style="position: absolute;left: 50%;top: 50%;transform: translate(-50%,-50%);width: 100%;z-index: 9;" />

		<!-- 多边形标记层 -->
		<canvas
			v-if="showPolygons && imageLoaded"
			canvas-id="polygonCanvas"
			:style="{
				position: 'absolute',
				left: '50%',
				top: '50%',
				transform: 'translate(-50%,-50%)',
				width: '100%',
				height: imageHeight + 'px',
				'z-index': 10,
				'pointer-events': 'none'
			}"
			:width="imageWidth"
			:height="imageHeight"
		></canvas>

		<!-- 弹窗区域 -->
		<regionContent :visible="regional!=''" :title="title" :progressData="skin.skin_data.result"></regionContent>
		<!-- 底部导航栏2 -->
		<view style="z-index: 5; "
			class="footer fixed bottom-0 left-0 w-full bg-[#fff] border-t border-[#f0f0f0] transition-transform duration-300 transform ios-bottom-safe">
			<scroll-view scroll-x="true" class="flex justify-around items-center h-16 px-4">
				<view class="chooseItem" v-for="item in menus" style="font-size: 28rpx;border-color: #F39196;"
					@click="chooseItem(item)">
					<image :src="item.act_icon" v-if="regional == item.faceMap" style="width: 50rpx;height: 50rpx; " />
					<image :src="item.icon" v-else style="width: 50rpx;height: 50rpx; " />
					<text class="text-[#6d6d6d] text-xs mt-1" :style="regional == item.faceMap ? 'color:rgb(255 143 156)' : ''">{{
						item.label }}</text>
				</view>
			</scroll-view>
		</view>
	</view>
</template>

<script setup>
import regionContent from "@/components/regionContent/regionContent.vue";
import { callAiPolling,skinAn } from "@/Api/index.js"
import { ref, onMounted,onBeforeUnmount, nextTick } from "vue"
const props = defineProps({
	peopleImg: {
		type: String,
		default: ''
	}
});
let timer = null
const emit = defineEmits([ 'update:loading', 'update:percent', 'show-login']);

// 新增变量用于多边形绘制
const imageLoaded = ref(false)
const imageWidth = ref(0)
const imageHeight = ref(0)
const showPolygons = ref(false)
onMounted(() => {
	// 检查用户是否已登录

	
	const userInfo = uni.getStorageSync('userInfo')
	if (!userInfo) {
		// 未登录，通知父组件显示登录弹窗
		emit('show-login')
		return
	}

	// 已登录，开始分析
	startAnalysis()
})

// 开始分析的方法
const startAnalysis = () => {
	emit('update:loading', true)

	let operationId = uni.getStorageSync('operationId')
	if (operationId) {
		getStatus(operationId)

		// timer = setInterval(async () => {
		// 	getStatus(operationId)
		// }, 3000)
	}
}
onBeforeUnmount(() => {

	clearInterval(timer)
})
let menus = ref([
	{
		label: "油光图",
		icon: "/static/imgs/油光图.png",
		act_icon: "/static/imgs/油光图-act.png",
		faceMap: "texture_enhanced_oily_area",
		dataLabel:'oily_intensity'
	},
	{
		label: "水分图",
		icon: "/static/imgs/水分图.png",
		act_icon: "/static/imgs/水分图-act.png",
		faceMap: "water_area",
		dataLabel:'water'
	},
	{
		label: "毛孔图",
		icon: "/static/imgs/毛孔图.png",
		act_icon: "/static/imgs/毛孔图-act.png",
		faceMap: "rough_area"
	},
	{
		label: "红区图",
		icon: "/static/imgs/红区图.png",
		act_icon: "/static/imgs/红区图-act.png",
		faceMap: "red_area"
	},
	{
		label: "色沉图",
		icon: "/static/imgs/色沉图.png",
		act_icon: "/static/imgs/色沉图-act.png",
		faceMap: "brown_area"
	},
	{
		label: "痤疮图",
		icon: "/static/imgs/痤疮图.png",
		act_icon: "/static/imgs/痤疮图-act.png",
		faceMap: "roi_outline_map"
	},
	{
		label: "皱纹图",
		icon: "/static/imgs/皱纹图.png",
		act_icon: "/static/imgs/皱纹图-act.png",
		faceMap: "texture_enhanced_lines"
	},
	{
		label: "黑头图",
		icon: "/static/imgs/黑头图.png",
		act_icon: "/static/imgs/黑头图-act.png",
		faceMap: "texture_enhanced_blackheads"
	},
	{
		label: "色斑图",
		icon: "/static/imgs/粗糙图.png",
		act_icon: "/static/imgs/粗糙图-act.png",
		faceMap: ""
	},
	{
		label: "闭口粉刺图",
		icon: "/static/imgs/粗糙图.png",
		act_icon: "/static/imgs/粗糙图-act.png",
		faceMap: ""
	},
	{
		label: "痣图",
		icon: "/static/imgs/粗糙图.png",
		act_icon: "/static/imgs/粗糙图-act.png",
		faceMap: ""
	},
])
let title = ref('')
let regional = ref('')
let regionalImg = ref('')
// 将白色背景转换为透明背景的函数
function convertWhiteToTransparent(base64Data) {
	return new Promise((resolve) => {
		// 创建一个canvas元素
		const canvas = document.createElement('canvas');
		const ctx = canvas.getContext('2d');

		// 创建一个Image对象
		const img = new Image();
		img.onload = function () {
			// 设置canvas尺寸
			canvas.width = img.width;
			canvas.height = img.height;

			// 绘制图片到canvas
			ctx.drawImage(img, 0, 0);

			// 获取图片数据
			const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
			const data = imageData.data;

			// 遍历每个像素，将白色（或接近白色）的像素设置为透明
			for (let i = 0; i < data.length; i += 4) {
				const r = data[i];     // 红色
				const g = data[i + 1]; // 绿色
				const b = data[i + 2]; // 蓝色
				const a = data[i + 3]; // 透明度

				// 检查是否为白色或接近白色（容差值可以调整）
				const tolerance = 240; // 容差值，可以根据需要调整
				if (r > tolerance && g > tolerance && b > tolerance) {
					data[i + 3] = 0; // 设置为完全透明
				}
			}

			// 将修改后的数据放回canvas
			ctx.putImageData(imageData, 0, 0);

			// 转换为base64格式（PNG格式支持透明度）
			const transparentBase64 = canvas.toDataURL('image/png');
			resolve(transparentBase64);
		};

		// 设置图片源
		img.src = `data:image/jpeg;base64,${base64Data}`;
	});
}

// 图片加载完成事件
function onImageLoad(e) {
	imageWidth.value = e.detail.width
	imageHeight.value = e.detail.height
	imageLoaded.value = true
}

// 绘制多边形
function drawPolygons() {
	if (!imageLoaded.value || !showPolygons.value) return

	const ctx = uni.createCanvasContext('polygonCanvas')
	ctx.clearRect(0, 0, imageWidth.value, imageHeight.value)

	let polygonData = []

	// 根据title确定要绘制的多边形数据
	if (title.value === '色斑图' && skin.value.skin_data.result.brown_spot) {
		polygonData = skin.value.skin_data.result.brown_spot.polygon
		console.log('res');
		
		ctx.setStrokeStyle('#FF6B6B') // 红色边框
		ctx.setFillStyle('rgba(255, 107, 107, 0.3)') // 半透明红色填充
		ctx.setLineWidth(2) // 设置线条宽度
	} else if (title.value === '闭口粉刺图' && skin.value.skin_data.result.closed_comedones) {
		polygonData = skin.value.skin_data.result.closed_comedones.polygon
		ctx.setStrokeStyle('#4ECDC4') // 青色边框
		ctx.setFillStyle('rgba(78, 205, 196, 0.3)') // 半透明青色填充
		ctx.setLineWidth(2) // 设置线条宽度
	} else if (title.value === '痣图' && skin.value.skin_data.result.mole) {
		polygonData = skin.value.skin_data.result.mole.polygon
		ctx.setStrokeStyle('#45B7D1') // 蓝色边框
		ctx.setFillStyle('rgba(69, 183, 209, 0.3)') // 半透明蓝色填充
		ctx.setLineWidth(2) // 设置线条宽度
	}

	// 绘制每个多边形
	polygonData.forEach(polygon => {
		if (polygon && polygon.length > 0) {
			ctx.beginPath()
			polygon.forEach((point, index) => {
				if (point.x !== undefined && point.y !== undefined) {
					if (index === 0) {
						ctx.moveTo(point.x, point.y)
					} else {
						ctx.lineTo(point.x, point.y)
					}
				}
			})
			ctx.closePath()
			ctx.fill()
			ctx.stroke()
		}
	})

	ctx.draw()
}

function chooseItem(item) {
	title.value = item.label
	regional.value = item.faceMap

	// 检查是否是需要显示多边形的类型
	const polygonTypes = ['色斑图', '闭口粉刺图', '痣图']
	showPolygons.value = polygonTypes.includes(item.label)

	
	if (showPolygons.value) {
		// 如果是多边形类型，不显示区域图片，只绘制多边形
		regionalImg.value = ''
		nextTick(() => {
			drawPolygons()
		})
	} else {
		// 原有逻辑：显示区域图片
		const base64Data = skin.value.skin_data.result.face_maps[regional.value]

		convertWhiteToTransparent(base64Data).then(transparentImg => {
			regionalImg.value = transparentImg;
		}).catch(error => {
			console.error('转换图片失败:', error);
			// 如果转换失败，使用原始图片
			regionalImg.value = `data:image/jpeg;base64,${base64Data}`;
		})
	}
}


async function getStatus(operationId) {
	let { data } = await skinAn({ operationId, imageUrl:props.peopleImg })
	// const completedCount = data.data.filter(item => item.aiStatus == '1').length
  // const totalCount = data.data.length
  // const percent = Math.round((completedCount / totalCount) * 100)
	// // 更新进度
	// emit('update:percent', percent)

	// let status = data.data.every(item => item.aiStatus == '1')
	if (1) {
		clearInterval(timer)
		// 完成后隐藏loading
		emit('update:loading', false)
		emit('update:percent', 100)
		organizeData(data.data)
	}
}
let skin = ref({
	skin_data:{
		result:{}
	}
})
function organizeData(data) {

	skin.value = data
	console.log(skin.value);

}

// 暴露方法给父组件调用
defineExpose({
	startAnalysis
})
</script>

<style lang="scss" scoped>
.footer {
	::v-deep(.scroll-view) .uni-scroll-view-content {
		display: flex !important;
		flex-wrap: nowrap;

		.chooseItem {
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: center;
			width: 130rpx;
			flex-shrink: 0;
			min-width: 130rpx;
		}
	}
}
</style>